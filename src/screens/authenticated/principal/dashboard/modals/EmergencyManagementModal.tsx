import React, { forwardRef } from "react";
import { View, Text, StyleSheet, Dimensions, StatusBar } from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { Modalize } from "react-native-modalize";
import { PanGestureHandler, State } from "react-native-gesture-handler";
import Animated, {
  useSharedValue,
  useAnimatedGestureHandler,
  useAnimatedStyle,
  runOnJS,
  withSpring,
} from "react-native-reanimated";

const { height: screenHeight } = Dimensions.get("window");

const EmergencyManagementModal = forwardRef<Modalize>((_, ref) => {
  const translateY = useSharedValue(0);

  const closeModal = () => {
    ref?.current?.close();
  };

  const gestureHandler = useAnimatedGestureHandler({
    onStart: (_, context) => {
      context.startY = translateY.value;
    },
    onActive: (event, context) => {
      const newTranslateY = context.startY + event.translationY;
      if (newTranslateY >= 0) {
        translateY.value = newTranslateY;
      }
    },
    onEnd: (event) => {
      if (event.translationY > 100 || event.velocityY > 500) {
        runOnJS(closeModal)();
      } else {
        translateY.value = withSpring(0);
      }
    },
  });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  return (
    <Modalize
      ref={ref}
      modalTopOffset={-StatusBar.currentHeight || 0}
      modalHeight={screenHeight + (StatusBar.currentHeight || 0)}
      adjustToContentHeight={false}
      modalStyle={styles.modal}
      rootStyle={styles.modalRoot}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      withHandle={false}
      panGestureEnabled={false}
      HeaderComponent={
        <Animated.View style={[styles.header, animatedStyle]}>
          <PanGestureHandler onGestureEvent={gestureHandler}>
            <Animated.View style={styles.dragHandle}>
              <View style={styles.dragIndicator} />
            </Animated.View>
          </PanGestureHandler>
          <View style={styles.headerContent}>
            <MaterialIcons name="emergency" size={24} color="#FF5722" />
            <Text style={styles.headerTitle}>Emergency Management</Text>
          </View>
        </Animated.View>
      }
    >
      <View style={styles.container}>
        <Text style={styles.placeholder}>Emergency Management System</Text>
        <Text style={styles.subtitle}>Feature coming soon...</Text>
      </View>
    </Modalize>
  );
});

const styles = StyleSheet.create({
  modalRoot: {
    position: "absolute",
    top: -(StatusBar.currentHeight || 0),
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 99999,
    height: screenHeight + (StatusBar.currentHeight || 0),
  },
  modal: {
    backgroundColor: "white",
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
    marginTop: 0,
    height: "100%",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 10,
    zIndex: 99999,
  },
  header: {
    paddingTop: (StatusBar.currentHeight || 0) + 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
    backgroundColor: "white",
  },
  dragHandle: {
    position: "absolute",
    top: (StatusBar.currentHeight || 0) + 20,
    left: 0,
    right: 0,
    height: 30,
    justifyContent: "center",
    alignItems: "center",
    zIndex: 1,
  },
  dragIndicator: {
    width: 40,
    height: 4,
    backgroundColor: "#ccc",
    borderRadius: 2,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1a1a1a",
    marginLeft: 12,
  },
  container: {
    flex: 1,
    padding: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  placeholder: {
    fontSize: 18,
    fontWeight: "600",
    color: "#FF5722",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: "#666",
  },
});

export default EmergencyManagementModal;
